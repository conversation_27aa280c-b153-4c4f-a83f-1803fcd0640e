# 前端模板对接后台系统 TODO 清单

## 📋 总体规划

本项目是基于 Ant Design Pro 的前端模板，需要对接后台系统。以下是完整的对接工作清单，按优先级和依赖关系进行组织。

---

## 🚀 阶段一：后台接口对接准备工作

### 1. 获取后台API文档和规范
- [ ] 获取完整的后台API文档
- [ ] 了解接口规范（RESTful/GraphQL等）
- [ ] 确认认证方式（JWT/Session等）
- [ ] 了解数据格式和错误码规范
- [ ] 确认接口版本管理策略

### 2. 配置开发环境代理
- [ ] 修改 `config/proxy.ts`，配置本地开发环境的后台API代理
- [ ] 设置不同环境的代理配置（dev/test/pre）
- [ ] 配置CORS相关设置
- [ ] 测试代理连接是否正常

### 3. 更新请求基础配置
- [ ] 修改 `src/app.tsx` 中的 request 配置
- [ ] 设置正确的 baseURL
- [ ] 配置通用请求头（Content-Type、Accept等）
- [ ] 设置请求超时时间
- [ ] 配置请求拦截器和响应拦截器

### 4. 创建API类型定义
- [ ] 根据后台接口文档创建 TypeScript 类型定义
- [ ] 更新 `src/typings.d.ts` 或创建专门的类型文件
- [ ] 定义通用的响应格式类型
- [ ] 定义各业务模块的数据类型

---

## 🔐 阶段二：用户认证系统对接

### 1. 对接登录接口
- [ ] 替换 `src/services/ant-design-pro/login.ts` 中的登录接口
- [ ] 实现用户名密码登录
- [ ] 实现第三方登录（如需要）
- [ ] 处理登录成功后的跳转逻辑
- [ ] 实现记住密码功能（如需要）

### 2. 对接用户信息接口
- [ ] 替换 `src/services/ant-design-pro/api.ts` 中的 currentUser 接口
- [ ] 获取真实用户信息（头像、姓名、权限等）
- [ ] 更新用户信息显示组件
- [ ] 实现用户信息修改功能

### 3. 实现Token管理
- [ ] 实现 JWT Token 的本地存储
- [ ] 实现 Token 自动添加到请求头
- [ ] 实现 Token 过期检测和自动刷新
- [ ] 实现 Token 失效时的自动登出
- [ ] 处理多标签页的 Token 同步

### 4. 对接注册接口
- [ ] 实现用户注册功能
- [ ] 对接后台注册相关接口
- [ ] 实现邮箱/手机验证码功能
- [ ] 处理注册成功后的流程

### 5. 对接登出接口
- [ ] 实现用户登出功能
- [ ] 清理本地存储的用户信息和Token
- [ ] 清理应用状态
- [ ] 跳转到登录页面

---

## 📊 阶段三：业务数据接口对接

### 1. 对接仪表板数据接口
- [ ] 替换 `mock/analysis.mock.ts` 中的分析数据
- [ ] 对接销售数据统计接口
- [ ] 对接访问量统计接口
- [ ] 对接实时数据接口
- [ ] 实现图表数据的动态更新

### 2. 对接表格列表接口
- [ ] 实现表格数据的分页查询
- [ ] 实现搜索和筛选功能
- [ ] 实现排序功能
- [ ] 实现数据的增删改查操作
- [ ] 处理批量操作功能

### 3. 对接表单提交接口
- [ ] 实现基础表单的提交功能
- [ ] 实现分步表单的数据提交
- [ ] 实现高级表单的复杂数据提交
- [ ] 处理表单验证和错误提示
- [ ] 实现表单数据的回显和编辑

### 4. 对接文件上传接口
- [ ] 实现图片上传功能
- [ ] 实现文档上传功能
- [ ] 实现多文件上传
- [ ] 实现上传进度显示
- [ ] 处理文件大小和格式限制

### 5. 对接通知消息接口
- [ ] 替换 `mock/notices.ts` 中的通知数据
- [ ] 实现系统通知功能
- [ ] 实现消息提醒功能
- [ ] 实现消息的已读/未读状态
- [ ] 实现消息的删除和清空功能

---

## 🔒 阶段四：权限系统集成

### 1. 实现基于角色的权限控制
- [ ] 根据后台返回的用户角色实现权限控制
- [ ] 更新权限判断逻辑
- [ ] 实现动态菜单显示
- [ ] 处理权限变更时的页面更新

### 2. 更新路由守卫
- [ ] 修改 `src/access.ts`，实现路由级别的权限控制
- [ ] 实现页面访问权限检查
- [ ] 处理无权限访问时的跳转
- [ ] 实现权限缓存机制

### 3. 实现组件级别权限控制
- [ ] 实现按钮级别的权限控制
- [ ] 实现菜单项的权限显示/隐藏
- [ ] 实现表格操作列的权限控制
- [ ] 创建权限控制的通用组件

### 4. 对接权限管理接口
- [ ] 实现角色管理功能
- [ ] 实现权限分配功能
- [ ] 实现用户角色绑定
- [ ] 实现权限的动态更新

---

## 🛠️ 阶段五：错误处理和用户体验优化

### 1. 完善请求错误处理
- [ ] 优化 `src/requestErrorConfig.ts`
- [ ] 完善HTTP错误码处理
- [ ] 实现用户友好的错误提示
- [ ] 处理网络异常情况
- [ ] 实现错误日志收集

### 2. 添加加载状态管理
- [ ] 为各个接口请求添加加载状态
- [ ] 实现全局加载状态管理
- [ ] 优化页面切换的加载体验
- [ ] 实现骨架屏加载效果

### 3. 实现请求重试机制
- [ ] 为关键接口实现自动重试
- [ ] 设置重试次数和间隔
- [ ] 处理重试失败的情况
- [ ] 实现指数退避重试策略

### 4. 优化网络请求性能
- [ ] 实现请求缓存机制
- [ ] 实现请求防抖和节流
- [ ] 取消重复请求
- [ ] 实现请求队列管理

---

## 🧪 阶段六：测试和部署配置

### 1. 编写单元测试
- [ ] 为API服务编写单元测试
- [ ] 为权限控制逻辑编写测试
- [ ] 为关键组件编写测试
- [ ] 实现测试覆盖率检查

### 2. 编写集成测试
- [ ] 编写登录流程的集成测试
- [ ] 编写业务流程的集成测试
- [ ] 编写权限控制的集成测试
- [ ] 实现自动化测试流程

### 3. 配置生产环境
- [ ] 配置生产环境的API地址
- [ ] 优化生产构建配置
- [ ] 配置CDN和静态资源
- [ ] 实现环境变量管理

### 4. 性能优化
- [ ] 实现代码分割和懒加载
- [ ] 优化打包体积
- [ ] 实现缓存策略
- [ ] 进行性能监控配置

---

## 📝 注意事项

### 开发规范
- 保持代码风格一致，使用项目配置的 Biome 进行代码检查
- 所有接口调用都要有错误处理
- 重要操作需要添加确认提示
- 敏感数据不要在前端存储

### 安全考虑
- Token 存储要考虑安全性
- 敏感操作需要二次验证
- 防止 XSS 和 CSRF 攻击
- 输入数据要进行验证和过滤

### 用户体验
- 所有异步操作都要有加载状态
- 错误提示要用户友好
- 重要操作要有成功反馈
- 页面切换要流畅

### 兼容性
- 确保在目标浏览器中正常运行
- 考虑移动端适配
- 处理网络不稳定的情况
- 考虑低版本浏览器的兼容性

---

## 🎯 里程碑

- **里程碑1**：完成基础配置和认证系统对接（预计1-2周）
- **里程碑2**：完成主要业务接口对接（预计2-3周）
- **里程碑3**：完成权限系统集成（预计1周）
- **里程碑4**：完成优化和测试（预计1-2周）

**总预计时间：5-8周**

---

*最后更新时间：2025-09-05*
